/* Dracula主题 */
:root {
    /* 基础色板 */
    --color-white: #F8F8F2;
    /* Dracula Foreground */
    --color-white-soft: rgba(248, 248, 242, 0.8);
    --color-white-mute: rgba(248, 248, 242, 0.6);
    --color-black: #282A36;
    /* Dracula Background */
    --color-black-soft: #343746;
    /* 稍暗背景 */
    --color-black-mute: #44475A;
    /* Dracula Current Line */

    /* 灰度阶梯 */
    --color-gray-1: #6272A4;
    /* Dracula Comment */
    --color-gray-2: #44475A;
    /* Current Line */
    --color-gray-3: #343746;
    /* 深灰 */

    /* 文本色阶 */
    --color-text-1: #F8F8F2;
    /* Foreground */
    --color-text-2: rgba(248, 248, 242, 0.7);
    --color-text-3: rgba(248, 248, 242, 0.5);

    /* 背景系统 */
    --color-background: var(--color-black);
    --color-background-soft: var(--color-black-soft);
    --color-background-mute: var(--color-black-mute);
    --color-background-opacity: rgba(40, 42, 54, 0.9);
    /* Dracula背景+透明度 */

    /* 主色系统 */
    --color-primary: #BD93F9;
    /* Dracula Purple */
    --color-primary-soft: #BD93F999;
    --color-primary-mute: #BD93F933;

    /* 图标与边框 */
    --color-icon: rgba(248, 248, 242, 0.6);
    --color-icon-white: #F8F8F2;
    --color-border: rgba(248, 248, 242, 0.1);
    --color-border-soft: rgba(248, 248, 242, 0.08);
    --color-border-mute: rgba(248, 248, 242, 0.05);

    /* 网格线颜色 */
    --color-grid: rgba(98, 114, 164, 0.15);
    /* Dracula Comment颜色半透明 */

    /* 功能色 */
    --color-error: #FF5555;
    /* Dracula Red */
    --color-link: #8BE9FD;
    /* Dracula Cyan */
    --color-code-background: #212235;
    /* 更鲜明的代码块背景色 */

    /* 交互状态 */
    --color-hover: rgba(68, 71, 90, 0.5);
    /* Current Line 50%透明度 */
    --color-active: rgba(68, 71, 90, 0.8);

    /* 组件特定 */
    --color-frame-border: #6272A4;
    /* Comment颜色 */
    --color-group-background: var(--color-black-soft);
    --color-reference: #44475A;
    --color-reference-text: #F8F8F2;
    --color-reference-background: #282A36;

    /* 导航栏与聊天 */
    --navbar-background-mac: rgba(40, 42, 54, 0.8);
    --navbar-background: #282A36;
    --chat-background: #282A36;
    --chat-background-user: #44475A;
    /* Current Line */
    --chat-background-assistant: #343746;
    /* 深灰 */
    --chat-text-user: #F8F8F2;

    /* 字体设置 */
    --font-family-base: "Maple Mono", "思源黑体 CN", "Source Han Sans CN", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-family-mono: "Maple Mono", Consolas, "Courier New", monospace;
    --font-family-heading: "思源黑体 CN", "Source Han Sans CN", "Maple Mono", "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif;

    /* 字体大小 */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;

    /* 行高 */
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.8;

    /* 字重 */
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-bold: 600;
    --font-weight-extrabold: 700;
}

/* 添加网格线效果 */
#content-container #messages,
#content-container #chat {
    /* 聊天消息区域 - 网格背景 */
    background-image: linear-gradient(to right, var(--color-grid) 1px, transparent 1px),
        linear-gradient(to bottom, var(--color-grid) 1px, transparent 1px) !important;
    background-size: 25px 25px;
    background-color: var(--chat-background);
    /* 明确设置主体背景 */
}

/* 代码块样式优化 */
.markdown {
    pre .shiki {
        border: none !important;
        background-color: var(--color-code-background) !important;
    }

    pre {
        padding: 0 !important;
        border-radius: 12px !important;
        background: none !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
    }

    pre [class^="CodeBlockWrapper-"] {
        border-radius: 12px !important;
        overflow: hidden;
        border-left: 2px solid rgba(189, 147, 249, 0.5) !important;
    }

    pre [class^="CodeHeader-"] {
        border-radius: 12px 12px 0 0 !important;
        background-color: var(--color-black-soft) !important;
        border-bottom: none !important;
        margin-bottom: 0 !important;
    }

    pre [class^="CodeContent-"] {
        background-color: var(--color-code-background) !important;
        border-radius: 0 0 12px 12px !important;
        border-top: none !important;
        margin-top: 0 !important;
    }
}

/* 修改JSON和代码内容的显示样式 */
.message-bubble pre,
.message-content pre {
    background-color: #383A59 !important;
    border-radius: 8px !important;
    padding: 12px !important;
    border-left: 2px solid rgba(189, 147, 249, 0.5) !important;
    margin: 10px 0 !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
    color: #F1FA8C !important;
    /* 使用Dracula黄色提高字符串可见度 */
}

.message-bubble pre .key,
.message-content pre .key {
    color: #FF79C6 !important;
    /* Dracula粉色用于键名 */
}

.message-bubble pre span[data-type="number"],
.message-content pre span[data-type="number"] {
    color: #BD93F9 !important;
    /* Dracula紫色用于数字 */
}

/* 思考框样式 */
.ant-collapse-content,
.ant-collapse-content-box {
    background-color: rgba(33, 34, 53, 0.5) !important;
    /* 更淡的半透明背景 */
    border-radius: 0 0 8px 8px;
    backdrop-filter: blur(10px) !important;
    /* 高斯模糊效果 */
    -webkit-backdrop-filter: blur(10px) !important;
    /* Safari 兼容 */
    box-shadow: 0 4px 20px rgba(189, 147, 249, 0.15) !important;
    /* 更淡的紫色阴影 */
    border: none !important;
    /* 移除所有边框 */
    color: var(--color-white) !important;
    /* 确保文字颜色明亮 */
    transition: all 0.3s ease !important;
    /* 平滑过渡效果 */
}

/* 思考框整体容器样式 */
.ant-collapse-item {
    border-left: 2px solid rgba(189, 147, 249, 0.5) !important;
    /* 为整个折叠项添加左边框 */
    border-top: none !important;
    border-right: none !important;
    border-bottom: none !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    /* 确保边框不超出边界 */
    margin-bottom: 8px !important;
    /* 增加一点底部间距 */
    box-shadow: none !important;
    /* 移除可能的阴影 */
}

/* 整个折叠面板样式 */
.ant-collapse {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

/* 思考框标题样式增强 */
.ant-collapse-header {
    background-color: rgba(52, 55, 70, 0.9) !important;
    border-radius: 8px 8px 0 0 !important;
    border: none !important;
    /* 移除所有边框 */
    padding: 10px 16px !important;
    color: var(--color-white-soft) !important;
    /* 使用柔和的白色替代纯紫色 */
    font-weight: 500 !important;
    /* 稍微减轻字重 */
    letter-spacing: 0.5px !important;
}

/* 思考框悬停效果 */
.ant-collapse-item:hover .ant-collapse-header {
    background-color: rgba(62, 65, 85, 0.95) !important;
    color: var(--color-primary) !important;
    /* 悬停时才显示紫色文字 */
}

/* 思考框展开状态 */
.ant-collapse-item-active .ant-collapse-header {
    border-bottom: 1px solid rgba(189, 147, 249, 0.2) !important;
    /* 减淡分隔线 */
}

/* 思考框内容部分 */
.ant-collapse-content {
    border: none !important;
    /* 移除所有边框 */
}

.ant-collapse-content-box {
    border: none !important;
    /* 移除所有边框 */
}

/* 消息容器样式修正 */
.message-content {
    border: none !important;
    box-shadow: none !important;
}

/* 思考框周围的消息气泡 */
.message-bubble {
    border: none !important;
    box-shadow: none !important;
}

/* 输入框样式 */
.inputbar-container {
    background-color: var(--color-black-soft);
    border: 1px solid var(--color-border-soft);
    border-radius: 8px;
}

/* Bug fixes */
.bubble .message-user .message-action-button:hover {
    background-color: var(--color-background-mute);
}

/* 应用字体设置 */
body,
html,
#app {
    font-family: var(--font-family-base);
    font-size: var(--font-size-md);
    line-height: var(--line-height-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 代码字体设置 */
pre,
code,
.markdown code,
.message-content pre {
    font-family: var(--font-family-mono) !important;
    font-size: var(--font-size-sm) !important;
    line-height: var(--line-height-normal) !important;
}

/* 标题字体设置 */
h1,
h2,
h3,
h4,
h5,
h6,
.markdown h1,
.markdown h2,
.markdown h3,
.markdown h4,
.markdown h5,
.markdown h6 {
    font-family: var(--font-family-heading);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
}

/* 聊天消息字体设置 */
.message-content,
.message-bubble {
    font-family: var(--font-family-base);
    font-size: var(--font-size-md);
    line-height: var(--line-height-relaxed);
}

/* 输入框字体设置 */
.inputbar-container textarea,
.inputbar-container input {
    font-family: var(--font-family-base);
    font-size: var(--font-size-md);
}